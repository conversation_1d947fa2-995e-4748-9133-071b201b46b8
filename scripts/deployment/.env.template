# =============================================================================
# Environment Variables Template for Deployment Scripts
# =============================================================================
#
# Copy this file to .env and fill in your actual credentials:
# cp .env.template .env
#
# Then source the environment variables before running the scripts:
# source .env
#
# IMPORTANT: Never commit the .env file with actual credentials to version control!
# Add .env to your .gitignore file.
#
# =============================================================================

# -----------------------------------------------------------------------------
# NeonDB PostgreSQL Database Configuration
# -----------------------------------------------------------------------------
# These variables are used by connect_neondb.sh

# Database host (without protocol)
DB_HOST=your-neondb-host.aws.neon.tech

# Database user
DB_USER=your_database_user

# Database password
DB_PASSWORD=your_database_password

# Database name
DB_NAME=your_database_name

# SSL mode (usually 'require' for NeonDB)
DB_SSL_MODE=require

# Channel binding (usually 'require' for NeonDB)
DB_CHANNEL_BINDING=require

# -----------------------------------------------------------------------------
# Shinyapps.io Configuration
# -----------------------------------------------------------------------------
# These variables are used by deploy_shiny.R

# Your shinyapps.io account name
SHINY_ACCOUNT_NAME=your_account_name

# Your shinyapps.io token
SHINY_TOKEN=your_token_here

# Your shinyapps.io secret
SHINY_SECRET=your_secret_here

# -----------------------------------------------------------------------------
# Example with actual format (DO NOT USE THESE VALUES):
# -----------------------------------------------------------------------------
# DB_HOST=ep-example-pooler.c-2.us-east-2.aws.neon.tech
# DB_USER=neondb_owner
# DB_PASSWORD=npg_ExamplePassword123
# DB_NAME=neondb
# DB_SSL_MODE=require
# DB_CHANNEL_BINDING=require
# 
# SHINY_ACCOUNT_NAME=myusername
# SHINY_TOKEN=1234567890ABCDEF1234567890ABCDEF
# SHINY_SECRET=abcdefghijklmnopqrstuvwxyz1234567890ABCDEF
