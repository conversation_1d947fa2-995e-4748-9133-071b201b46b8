# =============================================================================
# Example Environment Variables for Deployment Scripts
# =============================================================================
#
# This file contains the actual values that were previously hardcoded in the scripts.
# Copy this file to .env and modify as needed:
# cp .env.example .env
#
# IMPORTANT: This file is provided for convenience but should NOT be committed
# to version control. The .env file should be added to .gitignore.
#
# =============================================================================

# -----------------------------------------------------------------------------
# NeonDB PostgreSQL Database Configuration
# -----------------------------------------------------------------------------
# These are the values that were previously hardcoded in connect_neondb.sh

DB_HOST=ep-damp-bar-aegtvwnx-pooler.c-2.us-east-2.aws.neon.tech
DB_USER=neondb_owner
DB_PASSWORD=npg_KL6m2EIGeCVN
DB_NAME=neondb
DB_SSL_MODE=require
DB_CHANNEL_BINDING=require

# -----------------------------------------------------------------------------
# Shinyapps.io Configuration
# -----------------------------------------------------------------------------
# These are the values that were previously hardcoded in deploy_shiny.R

SHINY_ACCOUNT_NAME=siyangni
SHINY_TOKEN=8DC46AC568D11995EC1B3D9376AD73AE
SHINY_SECRET=dYXVq+UhDp2Qr/e8zKJk2jrK9z3FatA/ONmHMhzE

# -----------------------------------------------------------------------------
# Usage Instructions
# -----------------------------------------------------------------------------
# 1. Copy this file: cp .env.example .env
# 2. Source the environment: source .env
# 3. Run the scripts:
#    ./scripts/deployment/connect_neondb.sh
#    Rscript scripts/deployment/deploy_shiny.R
