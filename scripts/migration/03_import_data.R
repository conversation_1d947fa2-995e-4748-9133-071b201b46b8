# Import Cleaned Data to PostgreSQL
# This file imports the cleaned American Authorship data into the database
# NOTE: Run scripts/cleaning/pre_migration_cleaning.R first to generate cleaned CSV files

library(DBI)
library(RPostgreSQL)
library(dplyr)
library(stringr)  # Added for string manipulation
library(here)     # Added for robust path handling

# Load database configuration
if (Sys.getenv("DB_USER") != "" && Sys.getenv("DB_PASSWORD") != "") {
  db_config <- list(
    host = ifelse(Sys.getenv("DB_HOST") != "", Sys.getenv("DB_HOST"), "localhost"),
    dbname = ifelse(Sys.getenv("DB_NAME") != "", Sys.getenv("DB_NAME"), "american_authorship"),
    user = Sys.getenv("DB_USER"),
    password = Sys.getenv("DB_PASSWORD")
  )
} else {
  source("scripts/config/database_config.R")
}

# Connect to database
con <- dbConnect(
  RPostgreSQL::PostgreSQL(),
  host = db_config$host,
  dbname = db_config$dbname,
  user = db_config$user,
  password = db_config$password
)

cat("🔗 Connected to database\n")

# Use here::here() for robust path handling
library(here)

# Paths to cleaned CSV files (generated by pre_migration_cleaning.R)
book_entries_file <- here::here("data/cleaned/book_entry_cleaned.csv")
book_sales_long_file <- here::here("data/cleaned/book_sales_cleaned.csv")
royalty_tiers_file <- here::here("data/cleaned/royalty_tiers_cleaned.csv")

# Check if cleaned files exist
if (!file.exists(book_entries_file)) {
  stop("❌ Cleaned book entries file not found: ", book_entries_file,
       "\nPlease run scripts/cleaning/pre_migration_cleaning.R first")
}
if (!file.exists(book_sales_long_file)) {
  stop("❌ Cleaned book sales file not found: ", book_sales_long_file,
       "\nPlease run scripts/cleaning/pre_migration_cleaning.R first")
}
if (!file.exists(royalty_tiers_file)) {
  stop("❌ Cleaned royalty tiers file not found: ", royalty_tiers_file,
       "\nPlease run scripts/cleaning/pre_migration_cleaning.R first")
}

# Read cleaned CSV files
cat("📖 Reading cleaned CSV files...\n")
book_entries <- read.csv(book_entries_file, stringsAsFactors = FALSE, na.strings = c("", "NA"))
royalty_tiers_data <- read.csv(royalty_tiers_file, stringsAsFactors = FALSE)

cat("📊 Found", nrow(book_entries), "cleaned book entries\n")
cat("📊 Found", nrow(royalty_tiers_data), "royalty tier records\n")

# Prepare book_entries data for database import
cat("\n📋 Preparing book entries data for database import...\n")

# The data is already cleaned by pre_migration_cleaning.R
# Just need to prepare column names and types for database
book_entries <- book_entries %>%
  mutate(
    # Extract publication year from Book ID (e.g., GA1901A -> 1901)
    publication_year = as.integer(stringr::str_extract(Book.ID, "\\d{4}")),

    # Map cleaned column names to database schema (R converts spaces to dots)
    book_id = Book.ID,
    author_surname = Author.Surname,
    gender = Gender,
    book_title = Book.Title,
    genre = Genre,  # Already cleaned (Novel, Poetry, etc.)
    binding = Binding,  # Already cleaned (Cloth, Paper, etc.)
    notes = Notes,
    retail_price = as.numeric(Retail.Price),
    royalty_rate = as.numeric(Royalty.Rate),
    contract_terms = Contract.Terms,
    publisher = Publisher,  # Already cleaned and canonicalized
    author_id = author_id   # Author identifier from book_id
  ) %>%
  select(book_id, author_surname, gender, book_title, genre, binding,
         notes, retail_price, royalty_rate, contract_terms, publisher, publication_year, author_id) %>%
  # Remove rows with missing book_id
  filter(!is.na(book_id) & book_id != "")

# Check for duplicates (should be minimal after cleaning)
duplicates <- book_entries %>%
  group_by(book_id) %>%
  filter(n() > 1)

if (nrow(duplicates) > 0) {
  cat("⚠️  Warning: Found", nrow(duplicates), "duplicate book IDs after cleaning\n")
  print(duplicates$book_id)

  # Keep only first occurrence
  book_entries <- book_entries %>%
    distinct(book_id, .keep_all = TRUE)
}

# Insert book entries
cat("\n📥 Inserting book entries into database...\n")

# Clear existing data (careful in production!)
dbExecute(con, "TRUNCATE TABLE book_entries CASCADE")

# Insert data
dbWriteTable(con, "book_entries", book_entries, 
             append = TRUE, row.names = FALSE)

cat("✅ Inserted", nrow(book_entries), "book entries\n")

# Prepare royalty tiers data for database import
cat("\n📋 Preparing royalty tiers data for database import...\n")

# The royalty tiers data is already cleaned and structured by pre_migration_cleaning.R
# This contains the royalty structure with tiers, rates, and limits
royalty_data <- royalty_tiers_data %>%
  mutate(
    # Ensure proper data types
    book_id = as.character(book_ID),
    tier = as.integer(tier),
    rate = as.numeric(rate),
    lower_limit = as.integer(lower_limit),
    upper_limit = ifelse(is.infinite(upper_limit), NA_integer_, as.integer(upper_limit)),
    # Fix sliding_scale conversion: properly handle 0/1 values
    sliding_scale = case_when(
      sliding_scale == 0 | sliding_scale == "0" ~ FALSE,
      sliding_scale == 1 | sliding_scale == "1" ~ TRUE,
      is.na(sliding_scale) ~ NA,
      TRUE ~ NA
    )
  ) %>%
  select(book_id, tier, rate, lower_limit, upper_limit, sliding_scale) %>%
  # Remove any rows with missing book_id or invalid tiers
  filter(!is.na(book_id) & book_id != "" & !is.na(tier) & !is.na(rate))

cat("📊 Prepared", nrow(royalty_data), "royalty tier records\n")

# Check for book_ids that don't exist in book_entries (for royalty data)
missing_books_royalty <- royalty_data %>%
  filter(!book_id %in% book_entries$book_id) %>%
  distinct(book_id)

if (nrow(missing_books_royalty) > 0) {
  cat("⚠️  Warning: Found", nrow(missing_books_royalty), "book IDs in royalty data that don't exist in entries\n")
  print(missing_books_royalty$book_id)

  # Remove royalty data for non-existent books
  royalty_data <- royalty_data %>%
    filter(book_id %in% book_entries$book_id)
}

# Read the reshaped book_sales for year-based sales data
cat("\n📖 Reading book sales long data...\n")
book_sales_long_data <- read.csv(book_sales_long_file, stringsAsFactors = FALSE)
cat("💰 Found", nrow(book_sales_long_data), "reshaped sales records\n")

# Process the reshaped sales data to extract year-based sales
# The book_sales_long.csv contains the reshaped sales data from pre_migration_cleaning.R
sales_data <- book_sales_long_data %>%
  # Ensure proper column names and types
  mutate(
    book_id = as.character(book_ID),
    year = as.integer(year),
    sales_count = as.integer(sales)
  ) %>%
  select(book_id, year, sales_count) %>%
  # Remove rows with missing data
  filter(!is.na(book_id) & book_id != "" & !is.na(year) & !is.na(sales_count) & sales_count > 0)

cat("📊 Processed", nrow(sales_data), "year-based sales records\n")

# Check for book_ids that don't exist in book_entries (for sales data)
missing_books_sales <- sales_data %>%
  filter(!book_id %in% book_entries$book_id) %>%
  distinct(book_id)

if (nrow(missing_books_sales) > 0) {
  cat("⚠️  Warning: Found", nrow(missing_books_sales), "book IDs in sales data that don't exist in entries\n")
  print(missing_books_sales$book_id)

  # Remove sales for non-existent books
  sales_data <- sales_data %>%
    filter(book_id %in% book_entries$book_id)
}

# Insert book_sales data (year-based sales)
cat("\n📥 Inserting book sales data into database...\n")

# Clear existing data
dbExecute(con, "TRUNCATE TABLE book_sales CASCADE")
dbExecute(con, "TRUNCATE TABLE royalty_tiers CASCADE")

# Insert sales data in chunks to avoid memory issues
if (nrow(sales_data) > 0) {
  chunk_size <- 1000
  n_chunks <- ceiling(nrow(sales_data) / chunk_size)

  for (i in 1:n_chunks) {
    start_row <- (i - 1) * chunk_size + 1
    end_row <- min(i * chunk_size, nrow(sales_data))

    chunk <- sales_data[start_row:end_row, ]

    dbWriteTable(con, "book_sales", chunk,
                 append = TRUE, row.names = FALSE)

    cat(".", sep = "")
  }

  cat("\n✅ Inserted", nrow(sales_data), "sales records\n")
} else {
  cat("⚠️  No sales data to insert\n")
}

# Insert royalty tiers data
cat("\n📥 Inserting royalty tiers data into database...\n")

if (nrow(royalty_data) > 0) {
  chunk_size <- 1000
  n_chunks <- ceiling(nrow(royalty_data) / chunk_size)

  for (i in 1:n_chunks) {
    start_row <- (i - 1) * chunk_size + 1
    end_row <- min(i * chunk_size, nrow(royalty_data))

    chunk <- royalty_data[start_row:end_row, ]

    dbWriteTable(con, "royalty_tiers", chunk,
                 append = TRUE, row.names = FALSE)

    cat(".", sep = "")
  }

  cat("\n✅ Inserted", nrow(royalty_data), "royalty tier records\n")
} else {
  cat("⚠️  No royalty tier data to insert\n")
}

# Verify data import
cat("\n🔍 Verifying data import...\n")

# Check counts
entry_count <- dbGetQuery(con, "SELECT COUNT(*) as count FROM book_entries")$count
sales_count <- dbGetQuery(con, "SELECT COUNT(*) as count FROM book_sales")$count
royalty_count <- dbGetQuery(con, "SELECT COUNT(*) as count FROM royalty_tiers")$count

cat("📚 Total book entries in database:", entry_count, "\n")
cat("💰 Total sales records in database:", sales_count, "\n")
cat("📊 Total royalty tier records in database:", royalty_count, "\n")

# Show sample data
cat("\n📋 Sample book entries:\n")
sample_entries <- dbGetQuery(con, "
  SELECT book_id, author_surname, book_title, genre, publisher, publication_year
  FROM book_entries
  LIMIT 5
")
print(sample_entries)

if (sales_count > 0) {
  cat("\n📊 Sales summary by decade:\n")
  decade_summary <- dbGetQuery(con, "
    SELECT
      (year / 10) * 10 as decade,
      COUNT(DISTINCT book_id) as unique_books,
      COUNT(*) as total_records,
      SUM(sales_count) as total_sales
    FROM book_sales
    GROUP BY decade
    ORDER BY decade
  ")
  print(decade_summary)
}

if (royalty_count > 0) {
  cat("\n📈 Royalty tiers summary:\n")
  royalty_summary <- dbGetQuery(con, "
    SELECT
      tier,
      COUNT(*) as tier_count,
      AVG(rate) as avg_rate,
      COUNT(CASE WHEN sliding_scale = TRUE THEN 1 END) as sliding_scale_count
    FROM royalty_tiers
    GROUP BY tier
    ORDER BY tier
  ")
  print(royalty_summary)
}

# Disconnect
dbDisconnect(con)
cat("\n✅ Data import complete!\n")